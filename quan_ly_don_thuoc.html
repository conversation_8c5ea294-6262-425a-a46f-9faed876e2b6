<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON><PERSON><PERSON><PERSON> l<PERSON> đơn thuốc</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 0;
        }
        .container {
            width: 90%;
            margin: 0 auto;
        }
        header {
            background-color: #2c3e50;
            color: white;
            padding: 1rem;
        }
        .main-content {
            display: flex;
            margin-top: 20px;
        }
        .prescription-list {
            flex: 2;
            border: 1px solid #ddd;
            padding: 15px;
            margin-right: 20px;
        }
        .form-container {
            flex: 1;
            border: 1px solid #ddd;
            padding: 15px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        th {
            background-color: #f2f2f2;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
        }
        input {
            width: 100%;
            padding: 8px;
            box-sizing: border-box;
        }
        button {
            background-color: #3498db;
            color: white;
            border: none;
            padding: 10px 15px;
            cursor: pointer;
            margin-right: 5px;
        }
        button:hover {
            background-color: #2980b9;
        }
        .modal {
            display: none;
            position: fixed;
            z-index: 1;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.4);
        }
        .modal-content {
            background-color: white;
            margin: 15% auto;
            padding: 20px;
            border: 1px solid #888;
            width: 30%;
        }
        .close {
            color: #aaa;
            float: right;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
        }
    </style>
</head>
<body>
    <header>
        <div class="container">
            <h1>Hệ thống quản lý đơn thuốc</h1>
        </div>
    </header>

    <div class="container">
        <div class="main-content">
            <div class="prescription-list">
                <h2>Danh sách đơn thuốc</h2>
                <table>
                    <thead>
                        <tr>
                            <th>Mã đơn thuốc</th>
                            <th>Ngày thêm</th>
                            <th>Mã dược sĩ</th>
                            <th>Mã khách hàng</th>
                            <th>Tổng tiền</th>
                            <th>Thao tác</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>DT001</td>
                            <td>15/05/2023</td>
                            <td>DS001</td>
                            <td>KH001</td>
                            <td>250000</td>
                            <td>
                                <button onclick="editPrescription('DT001')">Sửa</button>
                                <button onclick="openDeleteModal('DT001')">Xóa</button>
                                <button onclick="openPrintModal('DT001')">In</button>
                            </td>
                        </tr>
                        <tr>
                            <td>DT002</td>
                            <td>16/05/2023</td>
                            <td>DS002</td>
                            <td>KH003</td>
                            <td>180000</td>
                            <td>
                                <button onclick="editPrescription('DT002')">Sửa</button>
                                <button onclick="openDeleteModal('DT002')">Xóa</button>
                                <button onclick="openPrintModal('DT002')">In</button>
                            </td>
                        </tr>
                    </tbody>
                </table>
                <button style="margin-top: 10px;" onclick="newPrescription()">Thêm đơn thuốc mới</button>
            </div>

            <div class="form-container" id="prescriptionForm">
                <h2>Thêm/Sửa đơn thuốc</h2>
                <form id="addEditForm">
                    <div class="form-group">
                        <label for="ngayThemDon">Ngày thêm đơn:</label>
                        <input type="date" id="ngayThemDon" name="ngayThemDon">
                    </div>
                    <div class="form-group">
                        <label for="maDonThuoc">Mã đơn thuốc:</label>
                        <input type="text" id="maDonThuoc" name="maDonThuoc">
                    </div>
                    <div class="form-group">
                        <label for="maDuocSi">Mã dược sĩ:</label>
                        <input type="text" id="maDuocSi" name="maDuocSi">
                    </div>
                    <div class="form-group">
                        <label for="maKhachHang">Mã khách hàng:</label>
                        <input type="text" id="maKhachHang" name="maKhachHang">
                    </div>
                    <div class="form-group">
                        <label for="tongTien">Tổng tiền:</label>
                        <input type="number" id="tongTien" name="tongTien">
                    </div>
                    <button type="button" onclick="savePrescription()">Lưu</button>
                    <button type="button" onclick="cancelEdit()">Hủy</button>
                </form>
            </div>
        </div>
    </div>

    <!-- Modal xác nhận xóa -->
    <div id="deleteModal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="closeDeleteModal()">&times;</span>
            <h3>Xác nhận xóa</h3>
            <p>Bạn có chắc chắn muốn xóa đơn thuốc này?</p>
            <button onclick="deletePrescription()">Xác nhận</button>
            <button onclick="closeDeleteModal()">Hủy</button>
        </div>
    </div>

    <!-- Modal xác nhận in -->
    <div id="printModal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="closePrintModal()">&times;</span>
            <h3>Xác nhận in</h3>
            <p>Bạn có muốn in đơn thuốc này?</p>
            <button onclick="printPrescription()">Xác nhận</button>
            <button onclick="closePrintModal()">Hủy</button>
        </div>
    </div>

    <script>
        let currentPrescriptionId = null;

        function newPrescription() {
            document.getElementById('addEditForm').reset();
            currentPrescriptionId = null;
            document.getElementById('prescriptionForm').style.display = 'block';
        }

        function editPrescription(id) {
            // Trong thực tế, bạn sẽ lấy dữ liệu từ server
            // Đây chỉ là mô phỏng
            if (id === 'DT001') {
                document.getElementById('ngayThemDon').value = '2023-05-15';
                document.getElementById('maDonThuoc').value = 'DT001';
                document.getElementById('maDuocSi').value = 'DS001';
                document.getElementById('maKhachHang').value = 'KH001';
                document.getElementById('tongTien').value = '250000';
            } else if (id === 'DT002') {
                document.getElementById('ngayThemDon').value = '2023-05-16';
                document.getElementById('maDonThuoc').value = 'DT002';
                document.getElementById('maDuocSi').value = 'DS002';
                document.getElementById('maKhachHang').value = 'KH003';
                document.getElementById('tongTien').value = '180000';
            }
            
            currentPrescriptionId = id;
            document.getElementById('prescriptionForm').style.display = 'block';
        }

        function savePrescription() {
            // Trong thực tế, bạn sẽ gửi dữ liệu đến server
            alert('Đã lưu đơn thuốc thành công!');
            document.getElementById('addEditForm').reset();
            document.getElementById('prescriptionForm').style.display = 'block';
        }

        function cancelEdit() {
            document.getElementById('addEditForm').reset();
            document.getElementById('prescriptionForm').style.display = 'block';
        }

        function openDeleteModal(id) {
            currentPrescriptionId = id;
            document.getElementById('deleteModal').style.display = 'block';
        }

        function closeDeleteModal() {
            document.getElementById('deleteModal').style.display = 'none';
        }

        function deletePrescription() {
            // Trong thực tế, bạn sẽ gửi yêu cầu xóa đến server
            alert('Đã xóa đơn thuốc ' + currentPrescriptionId);
            closeDeleteModal();
        }

        function openPrintModal(id) {
            currentPrescriptionId = id;
            document.getElementById('printModal').style.display = 'block';
        }

        function closePrintModal() {
            document.getElementById('printModal').style.display = 'none';
        }

        function printPrescription() {
            // Trong thực tế, bạn sẽ tạo bản in
            alert('Đang in đơn thuốc ' + currentPrescriptionId);
            closePrintModal();
        }
    </script>
</body>
</html>