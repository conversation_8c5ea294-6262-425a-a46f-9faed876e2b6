# Kiến trúc Document-View?

Giản lược triển khai MVC với C++, chỉ giữ lại Model và View / Tích hợp View với Controller.

## Các biểu đồ

### Biểu đồ lớp

![<PERSON>iể<PERSON> đồ lớp với các lớp được biểu diễn bằng các hình chữ nhật](diagrams/class-structure-design.png)

Quan hệ giữa các thành phần

### Biểu đồ tuần tự

![Biểu đồ tuần tự trường hợp tạo Game mới (bấm nút New Game)](diagrams/seq-create-newgame.png)

Tạo game mới

![Biểu đồ tuần tự trường hợp đoán số (bấm nút với nhãn là giá trị số)](diagrams/seq-guess-number.png)

Đ<PERSON><PERSON> số

![Biểu đồ tuần tự khởi tạo các đối tượng](diagrams/seq-init-app.png)

Khởi tạo các đối tượng