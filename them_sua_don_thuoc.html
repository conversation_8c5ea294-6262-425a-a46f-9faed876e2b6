<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Thêm/S<PERSON>a đơn thuốc</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 0;
        }
        .container {
            width: 90%;
            margin: 0 auto;
        }
        header {
            background-color: #2c3e50;
            color: white;
            padding: 1rem;
        }
        .form-container {
            border: 1px solid #ddd;
            padding: 15px;
            margin-top: 20px;
            max-width: 600px;
            margin-left: auto;
            margin-right: auto;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
        }
        input {
            width: 100%;
            padding: 8px;
            box-sizing: border-box;
        }
        button {
            background-color: #3498db;
            color: white;
            border: none;
            padding: 10px 15px;
            cursor: pointer;
            margin-right: 5px;
        }
        button:hover {
            background-color: #2980b9;
        }
    </style>
</head>
<body>
    <header>
        <div class="container">
            <h1><PERSON><PERSON> thống quản lý đơn thuốc</h1>
        </div>
    </header>

    <div class="container">
        <div class="form-container">
            <h2>Thêm/Sửa đơn thuốc</h2>
            <form id="addEditForm">
                <div class="form-group">
                    <label for="ngayThemDon">Ngày thêm đơn:</label>
                    <input type="date" id="ngayThemDon" name="ngayThemDon">
                </div>
                <div class="form-group">
                    <label for="maDonThuoc">Mã đơn thuốc:</label>
                    <input type="text" id="maDonThuoc" name="maDonThuoc">
                </div>
                <div class="form-group">
                    <label for="maDuocSi">Mã dược sĩ:</label>
                    <input type="text" id="maDuocSi" name="maDuocSi">
                </div>
                <div class="form-group">
                    <label for="maKhachHang">Mã khách hàng:</label>
                    <input type="text" id="maKhachHang" name="maKhachHang">
                </div>
                <div class="form-group">
                    <label for="tongTien">Tổng tiền:</label>
                    <input type="number" id="tongTien" name="tongTien">
                </div>
                <button type="button" onclick="savePrescription()">Lưu</button>
                <button type="button" onclick="window.location.href='trang_chu.html'">Hủy</button>
            </form>
        </div>
    </div>

    <script>
        // Lấy ID đơn thuốc từ URL nếu có
        const urlParams = new URLSearchParams(window.location.search);
        const prescriptionId = urlParams.get('id');
        
        // Nếu có ID, tức là đang sửa đơn thuốc
        if (prescriptionId) {
            // Trong thực tế, bạn sẽ gửi yêu cầu đến server để lấy thông tin đơn thuốc
            // Đây chỉ là mô phỏng
            if (prescriptionId === 'DT001') {
                document.getElementById('ngayThemDon').value = '2023-05-15';
                document.getElementById('maDonThuoc').value = 'DT001';
                document.getElementById('maDuocSi').value = 'DS001';
                document.getElementById('maKhachHang').value = 'KH001';
                document.getElementById('tongTien').value = '250000';
            } else if (prescriptionId === 'DT002') {
                document.getElementById('ngayThemDon').value = '2023-05-16';
                document.getElementById('maDonThuoc').value = 'DT002';
                document.getElementById('maDuocSi').value = 'DS002';
                document.getElementById('maKhachHang').value = 'KH003';
                document.getElementById('tongTien').value = '180000';
            }
        }

        function savePrescription() {
            // Trong thực tế, bạn sẽ gửi dữ liệu đến server
            alert('Đã lưu đơn thuốc thành công!');
            window.location.href = 'trang_chu.html';
        }
    </script>
</body>
</html>