/* XPM */
static const char *const img_blackcat_xpm[] = {
/* columns rows colors chars-per-pixel */
"52 33 148 2 ",
"   c black",
".  c #010101",
"X  c #020202",
"o  c gray1",
"O  c #040404",
"+  c gray2",
"@  c #060606",
"#  c #070707",
"$  c gray3",
"%  c #090909",
"&  c gray4",
"*  c #0C0C0C",
"=  c gray6",
"-  c #111111",
";  c gray7",
":  c gray8",
">  c #151515",
",  c #161616",
"<  c #191919",
"1  c gray10",
"2  c #1B1B1B",
"3  c gray11",
"4  c gray12",
"5  c gray13",
"6  c #252525",
"7  c gray15",
"8  c #282828",
"9  c gray16",
"0  c #2C2C2C",
"q  c #323232",
"w  c gray20",
"e  c gray21",
"r  c #3A3A3A",
"t  c gray23",
"y  c #3C3C3C",
"u  c gray24",
"i  c #3F3F3F",
"p  c #414141",
"a  c gray26",
"s  c #434343",
"d  c #444444",
"f  c #464646",
"g  c gray29",
"h  c #4C4C4C",
"j  c gray30",
"k  c #4E4E4E",
"l  c gray31",
"z  c #515151",
"x  c gray32",
"c  c #555555",
"v  c #585858",
"b  c gray35",
"n  c #5B5B5B",
"m  c gray36",
"M  c #5F5F5F",
"N  c gray38",
"B  c #646464",
"V  c #686868",
"C  c gray42",
"Z  c #6C6C6C",
"A  c gray43",
"S  c gray44",
"D  c #717171",
"F  c gray47",
"G  c #797979",
"H  c gray48",
"J  c #7B7B7B",
"K  c #7C7C7C",
"L  c gray49",
"P  c #808080",
"I  c #818181",
"U  c gray51",
"Y  c #868686",
"T  c #888888",
"R  c #8B8B8B",
"E  c gray55",
"W  c gray56",
"Q  c #939393",
"!  c gray59",
"~  c #979797",
"^  c #989898",
"/  c gray60",
"(  c #9A9A9A",
")  c #9B9B9B",
"_  c #9D9D9D",
"`  c gray62",
"'  c #9F9F9F",
"]  c gray63",
"[  c #A2A2A2",
"{  c gray64",
"}  c #A4A4A4",
"|  c #A5A5A5",
" . c #A9A9A9",
".. c #AAAAAA",
"X. c #ACACAC",
"o. c #AEAEAE",
"O. c #AFAFAF",
"+. c #B1B1B1",
"@. c gray70",
"#. c #B6B6B6",
"$. c gray72",
"%. c #B9B9B9",
"&. c #BBBBBB",
"*. c #BCBCBC",
"=. c #C0C0C0",
"-. c #C1C1C1",
";. c gray76",
":. c #C6C6C6",
">. c gray78",
",. c #C8C8C8",
"<. c #CDCDCD",
"1. c #CECECE",
"2. c gray81",
"3. c #D0D0D0",
"4. c gray82",
"5. c gray84",
"6. c #D7D7D7",
"7. c #D8D8D8",
"8. c gray85",
"9. c #DADADA",
"0. c gray87",
"q. c gray88",
"w. c gray89",
"e. c #E4E4E4",
"r. c gray90",
"t. c #E7E7E7",
"y. c gray91",
"u. c gray92",
"i. c #ECECEC",
"p. c gray93",
"a. c #EEEEEE",
"s. c #EFEFEF",
"d. c gray94",
"f. c #F1F1F1",
"g. c gray95",
"h. c #F3F3F3",
"j. c #F4F4F4",
"k. c gray96",
"l. c #F6F6F6",
"z. c gray97",
"x. c #F8F8F8",
"c. c #F9F9F9",
"v. c gray98",
"b. c #FBFBFB",
"n. c gray99",
"m. c #FDFDFD",
"M. c #FEFEFE",
"N. c white",
/* pixels */
"N.N.N.N.N.N.N.N.N.N.N.N.N.N.N.N.N.N.N.N.N.N.N.N.N.N.N.N.N.N.N.N.N.N.N.N.N.N.N.N.N.N.N.N.N.N.N.N.N.N.N.N.",
"N.N.N.N.N.N.N.N.N.N.N.,.d O O n x.N.N.N.N.N.N.N.N.N.N.N.N.N.N.N.N.N.N.N.N.N.N.N.N.N.N.N.N.N.N.N.N.N.N.N.",
"N.N.N.N.N.N.N.N.N.N.&.O         -.N.N.N.N.N.N.N.N.N.N.N.N.N.N.N.N.N.N.N.N.N.N.N.N.N.N.N.N.N.N.N.N.N.N.N.",
"N.N.N.N.N.N.N.N.N.x.5     S | | l.N.N.N.N.N.N.N.N.N.N.N.N.N.N.N.N.N.N.N.N.N.N.N.N.N.N.N.N.N.N.N.N.N.N.N.",
"N.N.N.N.N.N.N.N.N.#.    F N.N.N.N.N.N.N.N.N.N.N.N.N.N.N.N.N.N.N.N.N.N.N.N.N.N.N.N.N.N.N.N.N.N.N.N.N.N.N.",
"N.N.N.N.N.N.N.N.N.K     0.N.N.N.N.N.N.N.N.N.N.N.N.N.N.N.N.N.N.N.N.N.N.N.N.N.N.N.N.N.N.N.N.N.N.N.N.N.N.N.",
"N.N.N.N.N.N.N.N.N.B     d.N.N.N.N.N.N.N.N.N.N.N.N.N.N.N.N.N.N.N.N.N.N.N.N.N.N.N.N.N.N.N.N.N.N.N.N.N.N.N.",
"N.N.N.N.N.N.N.N.N.Z     3.N.N.N.N.N.N.N.N.N.N.N.N.N.N.N.N.N.N.N.N.N.N.N.N.N.N.N.N.x.N.N.N.N.N.N.N.N.N.N.",
"N.N.N.N.N.N.N.N.N.W     R N.N.N.N.N.N.N.N.N.N.N.N.N.N.N.N.N.N.N.N.N.N.N.N.N.N.N.S o.N.N.N.N.N.N.N.N.N.N.",
"N.N.N.N.N.N.N.N.N.<.    7 N.N.N.N.N.N.N.N.N.N.N.N.N.N.N.N.N.N.N.N.N.N.N.N.N.d.U   K N.N.N.N.N.N.N.N.N.N.",
"N.N.N.N.N.N.N.N.N.N.8     ` N.N.N.N.N.N.N.N.N.N.N.N.N.N.N.N.N.N.N.N.N.N.N.) *     $ V N.N.N.N.N.N.N.N.N.",
"N.N.N.N.N.N.N.N.N.N.~     - 7.N.N.N.N.N.N.N.N.N.N.N.N.N.N.N.N.N.N.N.N.N.~             o.N.N.N.N.N.N.N.N.",
"N.N.N.N.N.N.N.N.N.N.x.8     < Z d a k B U [ ,.p.N.N.N.N.N.N.N.N.N.N.N.[ O             d N.N.N.N.N.N.N.N.",
"N.N.N.N.N.N.N.N.N.N.N.-.O                       > r M U [ *.<.5.<.| g                   ..N.N.N.N.N.N.N.",
"N.N.N.N.N.N.N.N.N.N.N.N.W                                                               0 N.N.N.N.N.N.N.",
"N.N.N.N.N.N.N.N.N.N.N.N.x.3                                                             a N.N.N.N.N.N.N.",
"N.N.N.N.N.N.N.N.N.N.N.N.*.                                                        > k N e.N.N.N.N.N.N.N.",
"N.N.N.N.N.N.N.N.N.N.N.N.V                                                       z l.N.N.N.N.N.N.N.N.N.N.",
"N.N.N.N.N.N.N.N.N.N.N.N.<                                                     $ p.N.N.N.N.N.N.N.N.N.N.N.",
"N.N.N.N.N.N.N.N.N.N.N.-.                                                      u N.N.N.N.N.N.N.N.N.N.N.N.",
"N.N.N.N.N.N.N.N.N.N.N.n                                                       z N.N.N.N.N.N.N.N.N.N.N.N.",
"N.N.N.N.N.N.N.N.N.N.3.                                                        a N.N.N.N.N.N.N.N.N.N.N.N.",
"N.N.N.N.N.N.N.N.N.l.w                                                         < N.N.N.N.N.N.N.N.N.N.N.N.",
"N.N.N.N.N.N.N.N.x.d                     0 &.w.e.3.o.K a           O             7.N.N.N.N.N.N.N.N.N.N.N.",
"N.N.N.N.N.N.N.N.)       $ r F 8       O 3.N.N.N.N.N.N.N.e     * ~ y.d.Q O       R N.N.N.N.N.N.N.N.N.N.N.",
"N.N.N.N.N.N.N.N.u   5 | d.N.N.e       R N.N.N.N.N.N.N.N.a     +.N.N.N.N.[ O     7 x.N.N.N.N.N.N.N.N.N.N.",
"N.N.N.N.N.N.N.d.O $ w.N.N.N.N.>     k N.N.N.N.N.N.N.N.N.3   N N.N.N.N.N.N.Y       [ N.N.N.N.N.N.N.N.N.N.",
"N.N.N.N.N.N.N.:.  z N.N.N.N.x.O   - y.N.N.N.N.N.N.N.N.e.  * e.N.N.N.N.N.N.N.U     < y.N.N.N.N.N.N.N.N.N.",
"N.N.N.N.N.N.N.o.  ~ N.N.N.N.N.7   c N.N.N.N.N.N.N.N.N.+.  S N.N.N.N.N.N.N.N.N.W     g x.N.N.N.N.N.N.N.N.",
"N.N.N.N.N.N.N.+.$ w.N.N.N.N.N.)   3 d.N.N.N.N.N.N.N.N.U   3.N.N.N.N.N.N.N.N.N.N.[ O   k d.N.N.N.N.N.N.N.",
"N.N.N.N.N.N.N.l.5.N.N.N.N.N.N.N.n   a #.x.N.N.N.N.N.N.F   ) N.N.N.N.N.N.N.N.N.N.N.-.<   w x.N.N.N.N.N.N.",
"N.N.N.N.N.N.N.N.N.N.N.N.N.N.N.N.x.F *   ..N.N.N.N.N.N.:.$ M N.N.N.N.N.N.N.N.N.N.N.N.y.V k d.N.N.N.N.N.N.",
"N.N.N.N.N.N.N.N.N.N.N.N.N.N.N.N.N.N.l.7.N.N.N.N.N.N.N.N.p.x.N.N.N.N.N.N.N.N.N.N.N.N.N.N.N.N.N.N.N.N.N.N."
};
